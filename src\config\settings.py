"""
统一配置管理模块
Unified Configuration Management Module

此模块整合了项目中所有的配置设置，避免配置分散在各个文件中。
This module consolidates all configuration settings in the project to avoid scattered configurations.
"""

from typing import Set, List, Optional
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    
    # 数据库连接配置
    DATABASE_URL: str = "mysql://zhanshu_ai:MhGfsiP2QjCxXktb@***********:3306/zhanshu_ai?charset=utf8mb4"
    
    # 测试数据库配置
    TEST_DATABASE_URL: Optional[str] = None
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class MinIOSettings(BaseSettings):
    """MinIO 对象存储配置"""
    
    # MinIO 基础配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_SECURE: bool = False
    
    # 超时和重试配置
    MINIO_CONNECT_TIMEOUT: int = 30
    MINIO_READ_TIMEOUT: int = 120
    MINIO_WRITE_TIMEOUT: int = 300
    MINIO_MAX_RETRIES: int = 3
    MINIO_RETRY_DELAY: float = 2.0
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class LLMSettings(BaseSettings):
    """大语言模型配置"""
    
    # OpenAI API 配置
    OPENAI_API_KEY: str
    OPENAI_API_BASE: str = "https://api.sensenova.cn/v1/llm/chat-completions"
    OPENAI_API_MODEL_MULTIMODAL: str = "SenseNova-V6-Pro"
    OPENAI_API_MODEL_LLM: str = "SenseNova-V6-Pro"
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class DifySettings(BaseSettings):
    """Dify RAG 服务配置"""
    
    # Dify API 配置
    DIFY_API_KEY: str
    DIFY_API_BASE: str = "http://************/v1"
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class AlibabaCloudSettings(BaseSettings):
    """阿里云服务配置"""
    
    # 阿里云文档解析API 配置
    ACCESS_KEY_ID: str
    ACCESS_KEY_SECRET: str
    ENDPOINT: str = "docmind-api.cn-hangzhou.aliyuncs.com"
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class VideoAPISettings(BaseSettings):
    """视频API配置"""
    
    # 存储桶配置
    BUCKET_NAME: str = "zhanshu"
    
    # 并发和性能配置
    MAX_CONCURRENT_ANALYSIS: int = 2
    ANALYSIS_TIMEOUT: int = 300  # 5分钟超时
    MAX_VIDEO_SIZE_MB: int = 20
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class FileExtensionsSettings(BaseSettings):
    """文件扩展名配置"""
    
    # 支持的文档格式
    SUPPORTED_DOCUMENT_EXTENSIONS: Set[str] = {
        "pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "xlsm"
    }
    
    # 支持的图片格式
    SUPPORTED_IMAGE_EXTENSIONS: Set[str] = {
        "jpg", "jpeg", "png", "bmp", "gif"
    }
    
    # 支持的视频格式
    SUPPORTED_VIDEO_EXTENSIONS: Set[str] = {
        "mp4", "avi", "mov", "mkv", "wmv", "flv", "webm"
    }
    
    # 所有允许的文件扩展名
    @property
    def ALLOW_FILE_EXTENSIONS(self) -> Set[str]:
        return (
            self.SUPPORTED_DOCUMENT_EXTENSIONS
            .union(self.SUPPORTED_IMAGE_EXTENSIONS)
            .union(self.SUPPORTED_VIDEO_EXTENSIONS)
        )
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class ApplicationSettings(BaseSettings):
    """应用程序配置"""
    
    # 基础配置
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    ENVIRONMENT: str = "development"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    RELOAD: bool = True
    WORKERS: int = 1
    
    # CORS 配置
    ALLOWED_ORIGINS: List[str] = ["*"]
    ALLOWED_METHODS: List[str] = ["*"]
    ALLOWED_HEADERS: List[str] = ["*"]
    
    # 安全配置
    SECRET_KEY: str = "your_secret_key_here_change_in_production"
    JWT_SECRET_KEY: Optional[str] = None
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 30
    
    # 文件上传配置
    UPLOAD_MAX_SIZE: str = "100MB"
    TEMP_DIR: str = "temp/"
    UPLOAD_DIR: str = "uploads/"
    
    # API 限流配置
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class RedisSettings(BaseSettings):
    """Redis 配置 (可选)"""
    
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class MonitoringSettings(BaseSettings):
    """监控和日志配置"""
    
    # Sentry 配置
    SENTRY_DSN: Optional[str] = None
    
    # 日志配置
    LOG_FILE_PATH: str = "logs/app.log"
    LOG_MAX_SIZE: str = "10MB"
    LOG_BACKUP_COUNT: int = 5
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


# ===== 统一配置类 =====

class Settings(BaseSettings):
    """统一配置类，整合所有配置"""
    
    # 各模块配置
    database: DatabaseSettings = DatabaseSettings()
    minio: MinIOSettings = MinIOSettings()
    llm: LLMSettings = LLMSettings()
    dify: DifySettings = DifySettings()
    alibaba_cloud: AlibabaCloudSettings = AlibabaCloudSettings()
    video_api: VideoAPISettings = VideoAPISettings()
    file_extensions: FileExtensionsSettings = FileExtensionsSettings()
    app: ApplicationSettings = ApplicationSettings()
    redis: RedisSettings = RedisSettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


# ===== 全局配置实例 =====

# 创建全局配置实例
settings = Settings()

# 为了向后兼容，保留原有的配置实例
database_settings = settings.database
minio_settings = settings.minio
llm_settings = settings.llm
dify_settings = settings.dify
alibaba_cloud_settings = settings.alibaba_cloud
video_api_settings = settings.video_api
file_extensions_settings = settings.file_extensions
app_settings = settings.app
redis_settings = settings.redis
monitoring_settings = settings.monitoring


# ===== Tortoise ORM 配置 =====

# Tortoise ORM 配置 - 作为模块级别的变量，方便 aerich 导入
TORTOISE_CONFIG = {
    "connections": {"default": settings.database.DATABASE_URL},
    "apps": {
        "models": {
            "models": [
                "src.models.video",
                "src.models.document",
                "aerich.models"  # aerich 迁移工具所需的模型
            ],
            "default_connection": "default",
        }
    },
}


# ===== 便捷函数 =====

def get_allow_file_extensions() -> Set[str]:
    """获取所有允许的文件扩展名"""
    return settings.file_extensions.ALLOW_FILE_EXTENSIONS


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.database.DATABASE_URL


def get_minio_config() -> dict:
    """获取MinIO配置"""
    return {
        "endpoint": settings.minio.MINIO_ENDPOINT,
        "access_key": settings.minio.MINIO_ACCESS_KEY,
        "secret_key": settings.minio.MINIO_SECRET_KEY,
        "secure": settings.minio.MINIO_SECURE,
    }


def is_debug_mode() -> bool:
    """检查是否为调试模式"""
    return settings.app.DEBUG


def get_cors_config() -> dict:
    """获取CORS配置"""
    return {
        "allow_origins": settings.app.ALLOWED_ORIGINS,
        "allow_methods": settings.app.ALLOWED_METHODS,
        "allow_headers": settings.app.ALLOWED_HEADERS,
    }
