import type { Metadata } from "next"
import { VideoMetadataForm } from "@/components/video-metadata-form"

export const metadata: Metadata = {
  title: "编辑视频信息 | 视频管理系统",
  description: "编辑视频元数据并提交到知识库",
}

export default async function VideoEditPage({ params }: { params: Promise<{ videoId: string }> }) {
  const { videoId } = await params
  return (
    <div className="container mx-auto max-w-4xl py-8">
      <h1 className="mb-8 text-3xl font-bold">视频内容编辑与提交</h1>
      <VideoMetadataForm videoId={videoId} />
    </div>
  )
}
