"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { LayoutDashboard, Film, ChevronLeft, ChevronRight, Video } from "lucide-react"
import { useMobile } from "@/hooks/use-mobile"

export default function Sidebar() {
  const pathname = usePathname()
  const isMobile = useMobile()
  const [collapsed, setCollapsed] = useState(false)

  // 在移动设备上自动折叠侧边栏
  useEffect(() => {
    setCollapsed(isMobile)
  }, [isMobile])

  const routes = [
    {
      label: "仪表盘",
      icon: LayoutDashboard,
      href: "/",
      active: pathname === "/",
    },
    {
      label: "视频列表",
      icon: Film,
      href: "/videos",
      active: pathname === "/videos" || pathname.startsWith("/videos/"),
    },
  ]

  return (
    <div
      className={cn(
        "flex h-full flex-col border-r bg-background/80 backdrop-blur-sm transition-all duration-300",
        collapsed ? "w-16" : "w-64",
      )}
    >
      <div className={cn("flex h-16 items-center border-b px-4", collapsed ? "justify-center" : "justify-between")}>
        {!collapsed && (
          <Link href="/" className="flex items-center gap-2 font-semibold">
            <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
              <Video className="h-4 w-4" />
            </div>
            <span className="text-lg font-bold tracking-tight">VideoHub</span>
          </Link>
        )}
        {collapsed && (
          <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
            <Video className="h-4 w-4" />
          </div>
        )}
      </div>
      <div className="flex-1 overflow-auto py-4">
        <nav className="grid items-start gap-2 px-2 text-sm font-medium">
          {routes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "group flex items-center gap-3 rounded-lg px-3 py-2.5 transition-all hover:bg-muted",
                route.active ? "bg-primary/10 text-primary" : "text-muted-foreground hover:text-foreground",
                collapsed ? "justify-center" : "",
              )}
              title={collapsed ? route.label : undefined}
            >
              <div
                className={cn(
                  "flex h-7 w-7 items-center justify-center rounded-md transition-colors",
                  route.active
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground group-hover:bg-primary/20 group-hover:text-primary",
                )}
              >
                <route.icon className="h-4 w-4" />
              </div>
              {!collapsed && <span>{route.label}</span>}
            </Link>
          ))}
        </nav>
      </div>
      <div className={cn("mt-auto p-4", collapsed ? "flex justify-center" : "")}>
        {!isMobile && (
          <Button
            variant="outline"
            size="icon"
            className="h-9 w-9 rounded-full border-dashed"
            onClick={() => setCollapsed(!collapsed)}
            title={collapsed ? "展开侧边栏" : "折叠侧边栏"}
          >
            {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>
    </div>
  )
}
