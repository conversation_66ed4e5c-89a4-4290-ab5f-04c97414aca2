import type { Metadata } from "next"
import { VideoList } from "@/components/video-list"
import { VideoUploadButton } from "@/components/video-upload-button"

export const metadata: Metadata = {
  title: "视频列表 | VideoHub",
  description: "视频管理系统视频列表管理界面",
}

export default function VideosPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">视频列表</h1>
          <p className="text-muted-foreground">管理和组织您的所有视频内容。</p>
        </div>
        <VideoUploadButton />
      </div>
      <VideoList />
    </div>
  )
}
