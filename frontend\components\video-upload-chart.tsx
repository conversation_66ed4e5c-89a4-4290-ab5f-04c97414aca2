"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const data = [
  { name: "1月", 教育: 40, 娱乐: 24 },
  { name: "2月", 教育: 30, 娱乐: 13 },
  { name: "3月", 教育: 20, 娱乐: 38 },
  { name: "4月", 教育: 27, 娱乐: 39 },
  { name: "5月", 教育: 18, 娱乐: 48 },
  { name: "6月", 教育: 23, 娱乐: 38 },
  { name: "7月", 教育: 34, 娱乐: 43 },
]

export function VideoUploadChart() {
  return (
    <Card className="col-span-4 hover-card">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">视频上传趋势</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
              barGap={8}
              barSize={20}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" vertical={false} />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fill: "rgba(0,0,0,0.6)", fontSize: 12 }}
              />
              <YAxis axisLine={false} tickLine={false} tick={{ fill: "rgba(0,0,0,0.6)", fontSize: 12 }} />
              <Tooltip
                cursor={{ fill: "rgba(0,0,0,0.05)" }}
                contentStyle={{
                  borderRadius: "8px",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                  border: "none",
                }}
              />
              <Legend wrapperStyle={{ paddingTop: "10px" }} iconType="circle" />
              <Bar dataKey="教育" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              <Bar dataKey="娱乐" fill="#10b981" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
