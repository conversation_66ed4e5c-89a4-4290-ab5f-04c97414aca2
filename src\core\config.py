from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    # 数据库连接 URL，从 .env 文件或环境变量中加载
    # MySQL 格式：mysql://user:password@host:port/database_name
    DATABASE_URL: str = "mysql://zhanshu_ai:MhGfsiP2QjCxXktb@***********:3306/zhanshu_ai?charset=utf8mb4" # 默认值，实际部署时应通过环境变量覆盖

    # 从根目录的env文件中读取环境变量
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
    
settings = Settings()

# Tortoise ORM 配置 - 作为模块级别的变量，方便 aerich 导入
TORTOISE_CONFIG = {
    "connections": {"default": settings.DATABASE_URL},
    "apps": {
        "models": {
            # 更新模型路径以适应新的按服务模块结构
            "models": [
                "src.models.video",
                "src.models.document",
                "aerich.models" # aerich 迁移工具所需的模型
            ],
            "default_connection": "default",
        }
    },
}