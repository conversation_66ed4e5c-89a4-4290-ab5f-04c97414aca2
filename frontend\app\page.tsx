import type { Metadata } from "next"
import { VideoStatsCards } from "@/components/video-stats-cards"
import { VideoDistributionChart } from "@/components/video-distribution-chart"
import { VideoUploadChart } from "@/components/video-upload-chart"

export const metadata: Metadata = {
  title: "仪表盘 | VideoHub",
  description: "视频管理系统数据可视化仪表盘",
}

export default function DashboardPage() {
  return (
    <div className="flex animate-fade-in flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">仪表盘</h1>
        <p className="text-muted-foreground">查看和管理您的视频数据和统计信息。</p>
      </div>

      <div className="flex items-center justify-end">
        <div className="text-sm text-muted-foreground">最后更新: {new Date().toLocaleDateString("zh-CN")}</div>
      </div>

      <VideoStatsCards />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        <VideoUploadChart />
        <VideoDistributionChart />
      </div>
    </div>
  )
}
