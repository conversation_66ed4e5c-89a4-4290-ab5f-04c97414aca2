import os
from pydantic_settings import BaseSettings, SettingsConfigDict
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
import asyncio
import json


# 加载环境变量

class LLMSettings(BaseSettings):
    # OpenAI API 设置
    OPENAI_API_KEY: str
    OPENAI_API_BASE: str = "https://api.sensenova.cn/v1/llm/chat-completions"
    OPENAI_API_MODEL_MULTIMODAL: str = "SenseNova-V6-Pro"
    OPENAI_API_MODEL_LLM: str = "SenseNova-V6-Pro"
    
    # 设置 OpenAI 客户端
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

# 实例化settings对象
settings = LLMSettings()

# LLM服务类
class LLMService:
    def __init__(self, temperature: float = 0.7, max_tokens: int = 1024):
        # 初始化多模态大模型 - 增加超时时间
        self.multimodal_model = ChatOpenAI(
            model=settings.OPENAI_API_MODEL_MULTIMODAL,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_API_BASE,
            request_timeout=120,  # 增加到2分钟
        )
        
        # 初始化LLM大模型
        self.llm_model = ChatOpenAI(
            model=settings.OPENAI_API_MODEL_LLM,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_API_BASE,
            request_timeout=30,
        )
        
        # 初始化视频分析提示词模班，调用LLM大模型根据多模态所解析内容，优化描述，提取分类和标签
        
        self.refinement_chain: Runnable = ChatPromptTemplate.from_messages([
            ("system", """
             你是一个视频分析师，你的任务是根据用户提供的视频内容，生成一段视频摘要、分类标签以及多个tag。你提供的视频摘要将用于RAG召回，保证摘要包含核心内容方便用户检索。categories和tags是一个JSON Array。

             请根据视频的实际内容进行分析，不要使用固定的模板。输出结果严格以JSON格式返回：
             {{
                "description": "根据视频实际内容生成的详细摘要，包含主要讲解内容和关键信息",
                "categories": [
                    "根据视频内容确定的主要分类1",
                    "根据视频内容确定的主要分类2",
                    "根据视频内容确定的主要分类3"
                ],
                "tags": [
                    "视频中提到的关键词1",
                    "视频中提到的关键词2",
                    "视频中提到的关键词3",
                    "视频中提到的关键词4",
                    "视频中提到的关键词5"
                ]
            }}

            重要提示：
            1. 请仔细分析提供的视频描述，根据实际内容生成摘要、分类和标签
            2. 不要使用固定的模板或示例内容
            3. 分类和标签应该反映视频的真实内容
            4. 不要输出任何多余的内容，仅输出纯文本JSON格式的数据，不要用代码块包裹JSON数据
             """),
            ("user", "视频标题: {title}, 视频描述: {description}"),
        ]) | self.llm_model | JsonOutputParser()
        
    # 使用LLM模型对视频内容进行优化
    async def refine_video_content(self, title: str, description: str) -> dict:
        """
        调用LLM模型对视频内容进行优化

        Args:
            title (str): 视频标题
            description (str): 视频描述

        Returns:
            str: 优化后的视频内容
        """
        try:
            # 初始化优化链
            refine_data = await self.refinement_chain.ainvoke({"title": title, "description": description})
            return refine_data
        except Exception as e:
            raise RuntimeError(f"视频优化失败: {e}")
        
    async def analyze_multimodal_campaign(self, video_url: str) -> str:
        """
        多模态大模型分析视频内容

        Args:
            video_url (str): 视频URL地址

        Returns:
            str: 视频分析结果
        """

        print(f"[LLM] 开始分析视频: {video_url}")

        # 定义视频分析的提示词模板 - 使用原始的 video_url 格式
        video_analysis_message = [
            HumanMessage(
                content=[
                    {"type": "text", "text": "分析这个视频详细，生成一段视频主要内容的摘要,你生成的内容将用于RAG召回，你需要描述所讲解的主要内容和画面内容"},
                    {"type": "video_url", "video_url": video_url}
                ]
            )
        ]

        try:
            print(f"[LLM] 调用多模态模型进行视频分析")
            # 直接调用多模态进行视频分析
            response = await self.multimodal_model.ainvoke(video_analysis_message)

            if response and hasattr(response, 'content') and response.content:
                print(f"[LLM] 分析成功，响应长度: {len(response.content)}")
                return response.content
            else:
                print(f"[LLM] 模型返回空响应: {response}")
                raise RuntimeError(f"多模态模型返回空响应")

        except RuntimeError as re:
            # 如果是我们自己抛出的RuntimeError，直接重新抛出
            raise
        except Exception as e:
            print(f"[LLM] 视频分析异常详情:")
            print(f"  - 异常类型: {type(e).__name__}")
            print(f"  - 异常消息: {str(e)}")
            print(f"  - 异常repr: {repr(e)}")
            print(f"  - 异常args: {e.args}")
            print(f"  - 视频URL: {video_url}")

            # 强制异常隔离 - 确保不会传递异常的URL
            try:
                error_str = str(e)
                error_type = type(e).__name__

                # 检查异常消息是否异常
                if error_str == video_url or error_str == f"'{video_url}'" or video_url in error_str:
                    print(f"[LLM] 警告：异常消息包含视频URL，进行隔离处理")
                    error_message = f"多模态模型调用失败，异常类型: {error_type}"
                else:
                    error_message = error_str

                # 限制错误消息长度，防止过长的异常信息
                if len(error_message) > 200:
                    error_message = error_message[:200] + "..."

            except Exception as format_error:
                print(f"[LLM] 异常格式化失败: {format_error}")
                error_message = f"多模态模型调用失败，无法获取详细错误信息"

            # 重新抛出异常，使用安全的错误信息
            raise RuntimeError(f"视频分析失败: {error_message}")

if __name__ == "__main__":
    llm_service = LLMService()
    async def main():
        # 测试视频分析

        # response = await llm_service.analyze_multimodal_campaign("http://************:9000/zhanshu-video/test.mp4")
        # print(response)
        
        title = "test.mp4"
        description = "视频主要展示了Python编程环境中的UV工具的使用。首先，通过命令行界面创建了一个新的项目目录并进入该目录。接着，使用uv init命令初始化了项目，生成了pyproject.toml文件，其中包含了项目的元数据如名称、版本和依赖等信息。然后演示了如何用uv添加和安装依赖包，例如requests库，并且展示了如何在项目中导入并使用这些库。此外，还介绍了如何运行Python脚本以及如何使用UV来管理开发环境和生产环境的依赖。整个视频中，背景是深色调的代码编辑器界面，文字和图标以白色和彩色显示，突出显示了代码和命令行操作。视频风格偏向教育性质，光线平均分布在整个屏幕上，没有特别的光影效果。"
        
        # 测试视频优化
        response = await llm_service.refine_video_content(title, description)
        # print(f"原始响应: {response}")
        print(f"JSON响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    
    asyncio.run(main())