"use client"

import { <PERSON><PERSON><PERSON>, Line, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const data = [
  { name: "周一", 观看次数: 4000, 完成率: 65 },
  { name: "周二", 观看次数: 3000, 完成率: 58 },
  { name: "周三", 观看次数: 2000, 完成率: 69 },
  { name: "周四", 观看次数: 2780, 完成率: 72 },
  { name: "周五", 观看次数: 1890, 完成率: 70 },
  { name: "周六", 观看次数: 2390, 完成率: 75 },
  { name: "周日", 观看次数: 3490, 完成率: 80 },
]

export function VideoTrendsChart() {
  return (
    <Card className="hover-card">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">视频观看趋势</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fill: "rgba(0,0,0,0.6)", fontSize: 12 }}
              />
              <YAxis
                yAxisId="left"
                axisLine={false}
                tickLine={false}
                tick={{ fill: "rgba(0,0,0,0.6)", fontSize: 12 }}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                domain={[0, 100]}
                axisLine={false}
                tickLine={false}
                tick={{ fill: "rgba(0,0,0,0.6)", fontSize: 12 }}
                unit="%"
              />
              <Tooltip
                contentStyle={{
                  borderRadius: "8px",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                  border: "none",
                }}
                formatter={(value, name) => {
                  if (name === "完成率") return [`${value}%`, name]
                  return [value, name]
                }}
              />
              <Legend wrapperStyle={{ paddingTop: "10px" }} iconType="circle" />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="观看次数"
                stroke="#3b82f6"
                strokeWidth={3}
                dot={{ r: 4, strokeWidth: 2 }}
                activeDot={{ r: 6, strokeWidth: 0, fill: "#3b82f6" }}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="完成率"
                stroke="#f59e0b"
                strokeWidth={3}
                dot={{ r: 4, strokeWidth: 2 }}
                activeDot={{ r: 6, strokeWidth: 0, fill: "#f59e0b" }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
