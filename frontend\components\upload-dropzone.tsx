"use client"

import type React from "react"

import { useState, useCallback } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, X, AlertCircle, CheckCircle2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { uploadVideo, parseVideo, type VideoUploadResponse } from "@/lib/api"

export function UploadDropzone() {
  const router = useRouter()
  const [isDragging, setIsDragging] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [currentStep, setCurrentStep] = useState<'upload' | 'processing' | 'complete'>('upload')
  const [uploadResponse, setUploadResponse] = useState<VideoUploadResponse | null>(null)
  const [statusMessage, setStatusMessage] = useState<string>("")
  const [showRetryButton, setShowRetryButton] = useState(false)

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)

    const files = e.dataTransfer.files
    if (files && files.length > 0) {
      validateAndSetFile(files[0])
    }
  }, [])

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      validateAndSetFile(files[0])
    }
  }, [])

  const validateAndSetFile = (file: File) => {
    // 检查文件类型
    const validTypes = ["video/mp4", "video/quicktime", "video/x-msvideo", "video/webm"]
    if (!validTypes.includes(file.type)) {
      setError("不支持的文件格式。请上传 MP4, MOV, AVI 或 WebM 格式的视频。")
      return
    }

    // 检查文件大小 (例如限制为 500MB)
    const maxSize = 500 * 1024 * 1024 // 500MB in bytes
    if (file.size > maxSize) {
      setError("文件过大。请上传小于 500MB 的视频。")
      return
    }

    setFile(file)
    setError(null)
  }

  const handleUpload = async () => {
    if (!file) return

    setIsUploading(true)
    setProgress(0)
    setError(null)
    setCurrentStep('upload')
    setStatusMessage("正在上传视频文件...")

    try {
      // Step 1: Upload video file
      console.log('开始上传视频文件:', file.name)
      const uploadResult = await uploadVideo(file, (progressEvent) => {
        if (progressEvent.lengthComputable) {
          // Progress from 0% to 95% during upload
          const uploadProgress = Math.round((progressEvent.loaded / progressEvent.total) * 95)
          setProgress(uploadProgress)
        }
      })

      console.log('视频上传成功:', uploadResult)
      // Store upload response for step 2
      setUploadResponse(uploadResult)

      // Pause at 95% and update status
      setProgress(95)
      setCurrentStep('processing')
      setStatusMessage("文件上传完成，正在进行视频分析，解析时长约15-30秒...")

      // Step 2: Parse video automatically with retry logic
      await parseVideoWithRetry({
        title: uploadResult.title,
        video_url: uploadResult.video_url,
        description: ""
      })

      // Complete the process
      setProgress(100)
      setCurrentStep('complete')
      setSuccess(true)
      setStatusMessage("视频分析完成！正在跳转到编辑页面...")

      // Navigate to edit page
      setTimeout(() => {
        router.push(`/videos/edit/${encodeURIComponent(uploadResult.title)}`)
      }, 1500)
    } catch (err) {
      console.error('上传或分析过程中发生错误:', err)
      const errorMessage = err instanceof Error ? err.message : "处理失败，请重试。"
      setError(errorMessage)
      setIsUploading(false)

      // 如果上传成功但分析失败，显示重试按钮
      if (uploadResponse) {
        setCurrentStep('processing')
        setProgress(95)
        setShowRetryButton(true)
        setStatusMessage("视频分析失败，您可以点击重试按钮重新分析")
      } else {
        setCurrentStep('upload')
        setProgress(0)
        setStatusMessage("")
        setShowRetryButton(false)
      }
    }
  }

  // 带重试机制的视频解析函数
  const parseVideoWithRetry = async (request: { title: string; video_url: string; description: string }, maxRetries = 3) => {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`开始视频分析 (尝试 ${attempt}/${maxRetries}):`, request)
        setStatusMessage(`正在进行视频分析 (尝试 ${attempt}/${maxRetries})，请耐心等待...`)

        const result = await parseVideo(request)
        console.log('视频分析成功:', result)
        return result
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('未知错误')
        console.error(`视频分析失败 (尝试 ${attempt}/${maxRetries}):`, lastError.message)

        if (attempt < maxRetries) {
          // 等待一段时间后重试
          const waitTime = attempt * 2000 // 2秒, 4秒, 6秒
          setStatusMessage(`分析失败，${waitTime/1000}秒后重试 (${attempt}/${maxRetries})...`)
          await new Promise(resolve => setTimeout(resolve, waitTime))
        }
      }
    }

    // 所有重试都失败了
    throw new Error(`视频分析失败 (已重试${maxRetries}次): ${lastError?.message || '未知错误'}`)
  }

  // 重试视频分析
  const retryAnalysis = async () => {
    if (!uploadResponse) return

    setIsUploading(true)
    setError(null)
    setShowRetryButton(false)
    setCurrentStep('processing')
    setProgress(95)
    setStatusMessage("正在重新进行视频分析...")

    try {
      await parseVideoWithRetry({
        title: uploadResponse.title,
        video_url: uploadResponse.video_url,
        description: ""
      })

      // Complete the process
      setProgress(100)
      setCurrentStep('complete')
      setSuccess(true)
      setStatusMessage("视频分析完成！正在跳转到编辑页面...")

      // Navigate to edit page
      setTimeout(() => {
        router.push(`/videos/edit/${encodeURIComponent(uploadResponse.title)}`)
      }, 1500)
    } catch (err) {
      console.error('重试分析失败:', err)
      const errorMessage = err instanceof Error ? err.message : "分析失败，请重试。"
      setError(errorMessage)
      setIsUploading(false)
      setShowRetryButton(true)
      setStatusMessage("视频分析失败，您可以点击重试按钮重新分析")
    }
  }

  const resetUpload = () => {
    setFile(null)
    setIsUploading(false)
    setProgress(0)
    setError(null)
    setSuccess(false)
    setCurrentStep('upload')
    setUploadResponse(null)
    setStatusMessage("")
    setShowRetryButton(false)
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-500 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-100">
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>{statusMessage || "视频处理完成！正在跳转到编辑页面..."}</AlertDescription>
        </Alert>
      )}

      {isUploading && statusMessage && !success && (
        <Alert className="border-blue-500 bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
          <Upload className="h-4 w-4" />
          <AlertDescription>{statusMessage}</AlertDescription>
        </Alert>
      )}

      {showRetryButton && !isUploading && (
        <Alert className="border-orange-500 bg-orange-50 text-orange-800 dark:bg-orange-900 dark:text-orange-100">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{statusMessage}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={retryAnalysis}
              className="ml-4"
            >
              重试分析
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div
        className={cn(
          "flex h-64 flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors",
          isDragging ? "border-primary bg-primary/10" : "border-muted-foreground/25",
          isUploading ? "pointer-events-none opacity-60" : "cursor-pointer",
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => !isUploading && !file && document.getElementById("video-upload")?.click()}
      >
        {!file ? (
          <>
            <div className="mb-4 rounded-full bg-primary/10 p-4">
              <Upload className="h-8 w-8 text-primary" />
            </div>
            <p className="mb-2 text-lg font-medium">将视频拖放到此处</p>
            <p className="text-sm text-muted-foreground">仅支持 MP4（20MB以内） 格式</p>
            <input
              id="video-upload"
              type="file"
              accept="video/mp4,video/quicktime,video/x-msvideo,video/webm"
              className="hidden"
              onChange={handleFileChange}
              disabled={isUploading}
            />
            <Button
              variant="outline"
              className="mt-4"
              onClick={(e) => {
                e.stopPropagation()
                document.getElementById("video-upload")?.click()
              }}
            >
              或点击选择文件
            </Button>
          </>
        ) : (
          <div className="w-full space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="rounded-full bg-primary/10 p-2">
                  <Upload className="h-4 w-4 text-primary" />
                </div>
                <div className="max-w-[250px] overflow-hidden text-ellipsis whitespace-nowrap">{file.name}</div>
              </div>
              {!isUploading && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation()
                    resetUpload()
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {isUploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{progress}%</span>
                  <span className="text-muted-foreground">
                    {currentStep === 'upload' && '上传中...'}
                    {currentStep === 'processing' && '分析中...'}
                    {currentStep === 'complete' && '完成'}
                  </span>
                </div>
                <Progress value={progress} className="h-2 w-full" />
              </div>
            )}
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-4">
        <Button variant="outline" onClick={() => router.push("/videos")}>
          取消
        </Button>
        {showRetryButton && !isUploading ? (
          <Button onClick={retryAnalysis} variant="default">
            重试分析
          </Button>
        ) : (
          <Button onClick={handleUpload} disabled={!file || isUploading || success || showRetryButton}>
            {isUploading ? "处理中..." : "开始上传"}
          </Button>
        )}
      </div>
    </div>
  )
}
