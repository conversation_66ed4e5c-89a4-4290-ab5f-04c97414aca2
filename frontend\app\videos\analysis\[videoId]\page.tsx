import type { Metadata } from "next"
import { VideoAnalysisForm } from "@/components/video-analysis-form"

export const metadata: Metadata = {
  title: "视频分析 | 视频管理系统",
  description: "AI视频内容分析处理",
}

export default async function VideoAnalysisPage({ params }: { params: Promise<{ videoId: string }> }) {
  const { videoId } = await params
  return (
    <div className="container mx-auto max-w-4xl py-8">
      <h1 className="mb-8 text-3xl font-bold">视频AI分析处理</h1>
      <VideoAnalysisForm videoId={videoId} />
    </div>
  )
}
