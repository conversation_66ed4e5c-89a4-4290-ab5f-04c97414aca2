// API调用的函数

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000'

// 视频数据类型定义
export interface Video {
  id: number
  title: string
  description: string
  video_url: string
  categories: string[]
  tags: string[]
  status: 'pending' | 'processing' | 'completed' | 'failed'
  created_at: string
  updated_at: string
}

// 分页信息类型
export interface PaginationInfo {
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 视频列表API响应类型
export interface VideoListResponse {
  code: number
  message: string
  videos: Video[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

/**
 * 获取视频列表
 * @param page 页码，默认1
 * @param pageSize 每页数量，默认10
 * @returns 视频列表和分页信息
 */
export async function getVideoList(page: number = 1, pageSize: number = 10): Promise<VideoListResponse> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    })

    const response = await fetch(`${API_BASE_URL}/api/video/list?${params}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: VideoListResponse = await response.json()

    if (data.code !== 200) {
      throw new Error(data.message || '获取视频列表失败')
    }

    return data
  } catch (error) {
    console.error('获取视频列表失败:', error)
    throw error
  }
}

// 视频上传API响应类型
export interface VideoUploadResponse {
  code: number
  message: string
  title: string
  video_url: string
}

// 视频分析请求类型
export interface VideoAnalysisRequest {
  title: string
  video_url: string
  description?: string
}

// 视频解析API响应类型 (parse_video)
export interface VideoParseResponse {
  code: number
  message: string
  response: string // 解析后的描述文本
}

// 视频分析API响应类型 (analyze_video)
export interface VideoAnalysisResponse {
  code: number
  message: string
  response: {
    description: string
    categories: string[]
    tags: string[]
  }
}

// 视频元数据类型 (用于edit页面)
export interface VideoMetadata {
  title: string
  video_url: string
  description: string
  categories: string[]
  tags: string[]
}

/**
 * 上传视频文件到后端API
 * @param file 视频文件
 * @param onProgress 进度回调函数
 * @returns 上传响应数据
 */
export async function uploadVideo(
  file: File,
  onProgress?: (progressEvent: { loaded: number; total: number; lengthComputable: boolean }) => void,
): Promise<VideoUploadResponse> {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('file', file)

    const xhr = new XMLHttpRequest()

    // 设置进度监听
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        onProgress({
          loaded: event.loaded,
          total: event.total,
          lengthComputable: true,
        })
      }
    })

    // 设置完成监听
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response: VideoUploadResponse = JSON.parse(xhr.responseText)
          if (response.code === 200) {
            resolve(response)
          } else {
            reject(new Error(response.message || '上传失败'))
          }
        } catch (error) {
          reject(new Error('解析响应失败'))
        }
      } else {
        reject(new Error(`上传失败: HTTP ${xhr.status}`))
      }
    })

    // 设置错误监听
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误，上传失败'))
    })

    // 发送请求
    xhr.open('POST', `${API_BASE_URL}/api/video/upload_video`)
    xhr.send(formData)
  })
}

/**
 * 调用视频解析API (第一步分析)
 * @param request 视频分析请求
 * @returns 解析结果
 */
export async function parseVideo(request: VideoAnalysisRequest): Promise<VideoParseResponse> {
  try {
    console.log('发送视频解析请求:', request)

    // 创建一个带超时的 AbortController
    const controller = new AbortController()
    const timeoutId = setTimeout(() => {
      controller.abort()
    }, 60000) // 60秒超时

    const response = await fetch(`${API_BASE_URL}/api/video/parse_video`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
      signal: controller.signal,
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`
      try {
        const errorData = await response.json()
        errorMessage = errorData.detail || errorData.message || errorMessage
      } catch {
        // 忽略JSON解析错误，使用默认错误消息
      }
      throw new Error(`视频解析请求失败: ${errorMessage}`)
    }

    const data: VideoParseResponse = await response.json()
    console.log('视频解析API响应:', data)

    if (data.code !== 200) {
      throw new Error(data.message || '视频解析失败')
    }

    return data
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        console.error('视频解析超时')
        throw new Error('视频解析超时，请检查网络连接或稍后重试')
      }
      console.error('视频解析失败:', error.message)
      throw error
    } else {
      console.error('视频解析失败:', error)
      throw new Error('视频解析失败，请重试')
    }
  }
}

/**
 * 调用视频深度分析API，生成精细化的元数据
 * @param request 视频分析请求
 * @returns 精细化分析结果
 */
export async function analyzeVideo(request: VideoAnalysisRequest): Promise<VideoAnalysisResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/video/analyze_video`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: VideoAnalysisResponse = await response.json()

    if (data.code !== 200) {
      throw new Error(data.message || '视频深度分析失败')
    }

    return data
  } catch (error) {
    console.error('视频深度分析失败:', error)
    throw error
  }
}

/**
 * 解码视频ID (处理URL编码问题)
 * @param videoId 可能被URL编码的视频ID
 * @returns 解码后的视频ID
 */
function decodeVideoId(videoId: string): string {
  try {
    // 处理常见的URL编码问题
    return decodeURIComponent(videoId)
  } catch {
    // 如果解码失败，返回原始值
    return videoId
  }
}

// 视频信息API响应类型
export interface VideoInfoResponse {
  code: number
  message: string
  data: {
    title: string
    video_url: string
    description: string
    categories: string[]
    tags: string[]
    status: string
  }
}

/**
 * 获取视频基础信息 (从数据库查询已存在的description)
 * @param videoId 视频ID
 * @returns 视频基础信息
 */
export async function getVideoInfo(videoId: string): Promise<{title: string, video_url: string, description: string}> {
  try {
    // 解码videoId以处理URL编码问题
    const decodedVideoId = decodeVideoId(videoId)

    // 调用后端API获取视频信息
    const response = await fetch(`${API_BASE_URL}/api/video/info/${encodeURIComponent(decodedVideoId)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: VideoInfoResponse = await response.json()

    if (data.code !== 200) {
      throw new Error(data.message || '获取视频信息失败')
    }

    return {
      title: data.data.title,
      video_url: data.data.video_url,
      description: data.data.description
    }
  } catch (error) {
    console.error('获取视频信息失败:', error)
    throw error
  }
}

/**
 * 获取视频元数据 - 使用真实API调用
 * @param videoId 视频ID (通常是文件名，如 test.mp4)
 * @returns 视频元数据
 */
export async function getVideoMetadata(videoId: string): Promise<VideoMetadata> {
  try {
    // 获取视频基础信息
    const videoInfo = await getVideoInfo(videoId)

    // 调用analyze_video API获取精细化元数据
    const analysisResult = await analyzeVideo({
      title: videoInfo.title,
      video_url: videoInfo.video_url,
      description: videoInfo.description
    })

    // 返回完整的元数据
    return {
      title: videoInfo.title,
      video_url: videoInfo.video_url,
      description: analysisResult.response.description,
      categories: analysisResult.response.categories,
      tags: analysisResult.response.tags
    }
  } catch (error) {
    console.error('获取视频元数据失败:', error)
    throw error
  }
}

// Dify提交响应类型
export interface DifySubmitResponse {
  code: number
  message: string
  data?: {
    knowledge_base_id: string
    document_id: string
  }
  error?: string
}

/**
 * 提交视频元数据到Dify知识库
 * 使用标准化JSON格式，确保数据符合要求
 * @param videoId 视频ID
 * @param metadata 元数据对象
 */
export async function submitVideoMetadata(
  videoId: string,
  metadata: {
    title: string
    description: string
    keywords: string[]
    tags: string[]
  },
) {
  try {
    // 数据验证和清理
    const cleanTitle = metadata.title.trim()
    const cleanDescription = metadata.description.trim()
    const cleanCategories = metadata.keywords.filter(k => k.trim()).map(k => k.trim())
    const cleanTags = metadata.tags.filter(t => t.trim()).map(t => t.trim())

    // 验证必填字段
    if (!cleanTitle) {
      throw new Error('视频标题不能为空')
    }

    if (!cleanDescription) {
      throw new Error('视频描述不能为空')
    }

    if (cleanDescription.length > 2000) {
      throw new Error('视频描述长度不能超过2000字符')
    }

    if (cleanCategories.length < 2 || cleanCategories.length > 5) {
      throw new Error('分类数量应为2-5个')
    }

    if (cleanTags.length < 3 || cleanTags.length > 10) {
      throw new Error('标签数量应为3-10个')
    }

    // 构建标准化的video_url
    const video_url = `http://************:9000/zhanshu-video/${videoId}`

    // 构建标准化的JSON payload
    const standardizedPayload = {
      title: cleanTitle,
      description: cleanDescription,
      categories: cleanCategories,
      tags: cleanTags,
      video_url: video_url,
    }

    const response = await fetch(`${API_BASE_URL}/api/video/submit_to_dify`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(standardizedPayload),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: DifySubmitResponse = await response.json()

    if (data.code === 400) {
      // 处理验证错误
      const errorMessage = data.error || data.message || '请求数据格式错误'
      throw new Error(errorMessage)
    }

    if (data.code !== 200) {
      throw new Error(data.message || '提交到知识库失败')
    }

    return {
      success: true,
      knowledge_base_id: data.data?.knowledge_base_id,
      document_id: data.data?.document_id,
      payload: standardizedPayload
    }
  } catch (error) {
    console.error('提交视频元数据到Dify失败:', error)
    throw error
  }
}

// 删除视频API响应类型
export interface DeleteVideoResponse {
  code: number
  message: string
  data: {
    video_id: string
    database_deleted: boolean
    dify_document_deleted: boolean
    dify_deletion_message: string
  }
}

/**
 * 删除视频记录和相关的知识库文档
 * @param videoId 视频ID
 * @returns 删除结果
 */
export async function deleteVideo(videoId: string): Promise<DeleteVideoResponse> {
  try {
    // 解码videoId以处理URL编码问题
    const decodedVideoId = decodeVideoId(videoId)

    const response = await fetch(`${API_BASE_URL}/api/video/delete/${encodeURIComponent(decodedVideoId)}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`
      try {
        const errorData = await response.json()
        errorMessage = errorData.detail || errorData.message || errorMessage
      } catch {
        // 忽略JSON解析错误，使用默认错误消息
      }
      throw new Error(`删除视频失败: ${errorMessage}`)
    }

    const data: DeleteVideoResponse = await response.json()

    if (data.code !== 200) {
      throw new Error(data.message || '删除视频失败')
    }

    return data
  } catch (error) {
    console.error('删除视频失败:', error)
    throw error
  }
}
