"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, X, AlertCircle, CheckCircle2 } from "lucide-react"
import { getVideoMetadata, submitVideoMetadata, getVideoInfo, analyzeVideo, type VideoMetadata } from "@/lib/api"

interface Tag {
  id: string
  text: string
}

export function VideoMetadataForm({ videoId }: { videoId: string }) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isAnalyzing, setIsAnalyzing] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [videoUrl, setVideoUrl] = useState<string | null>(null)

  // Third API call state
  const [isRefiningMetadata, setIsRefiningMetadata] = useState(false)
  const [hasRefinedMetadata, setHasRefinedMetadata] = useState(false)
  const [rawDescription, setRawDescription] = useState("") // Store the description from parse API

  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [keywordInput, setKeywordInput] = useState("")
  const [keywords, setKeywords] = useState<Tag[]>([])
  const [tagInput, setTagInput] = useState("")
  const [tags, setTags] = useState<Tag[]>([])

  // 获取视频元数据 - 使用真实API调用
  useEffect(() => {
    let isMounted = true

    const fetchVideoMetadata = async () => {
      try {
        setIsLoading(true)
        setIsRefiningMetadata(true)
        setError(null)

        // 获取视频基础信息
        const videoInfo = await getVideoInfo(videoId)

        if (!isMounted) return

        // 设置基本信息
        setTitle(videoInfo.title)
        setVideoUrl(videoInfo.video_url)
        setRawDescription(videoInfo.description)

        // 调用analyze_video API获取精细化元数据
        const analysisResult = await analyzeVideo({
          title: videoInfo.title,
          video_url: videoInfo.video_url,
          description: videoInfo.description
        })

        if (!isMounted) return

        // 更新表单数据
        setDescription(analysisResult.response.description)

        if (analysisResult.response.categories && Array.isArray(analysisResult.response.categories)) {
          setKeywords(analysisResult.response.categories.map((cat: string) => ({
            id: Math.random().toString(),
            text: cat
          })))
        }

        if (analysisResult.response.tags && Array.isArray(analysisResult.response.tags)) {
          setTags(analysisResult.response.tags.map((tag: string) => ({
            id: Math.random().toString(),
            text: tag
          })))
        }

        setHasRefinedMetadata(true)
        setIsRefiningMetadata(false)
        setIsLoading(false)

      } catch (err) {
        if (!isMounted) return
        setError(err instanceof Error ? err.message : "获取视频信息失败，请重试")
        setIsLoading(false)
        setIsRefiningMetadata(false)
      }
    }

    fetchVideoMetadata()

    return () => {
      isMounted = false
    }
  }, [videoId])

  // Function to manually re-run the analysis
  const refineVideoMetadata = async (title: string, videoUrl: string, description: string) => {
    try {
      setIsRefiningMetadata(true)
      setError(null)

      const analysisResult = await analyzeVideo({
        title,
        video_url: videoUrl,
        description
      })

      // Update form with refined metadata
      setDescription(analysisResult.response.description)

      if (analysisResult.response.categories && Array.isArray(analysisResult.response.categories)) {
        setKeywords(analysisResult.response.categories.map((cat: string) => ({
          id: Math.random().toString(),
          text: cat
        })))
      }

      if (analysisResult.response.tags && Array.isArray(analysisResult.response.tags)) {
        setTags(analysisResult.response.tags.map((tag: string) => ({
          id: Math.random().toString(),
          text: tag
        })))
      }

      setHasRefinedMetadata(true)
    } catch (err) {
      setError(err instanceof Error ? err.message : "元数据精细化分析失败")
    } finally {
      setIsRefiningMetadata(false)
    }
  }

  const addKeyword = () => {
    const trimmed = keywordInput.trim()
    if (trimmed && !keywords.some((k) => k.text.toLowerCase() === trimmed.toLowerCase())) {
      setKeywords([...keywords, { id: Math.random().toString(), text: trimmed }])
      setKeywordInput("")
    }
  }

  const removeKeyword = (id: string) => {
    setKeywords(keywords.filter((k) => k.id !== id))
  }

  const addTag = () => {
    const trimmed = tagInput.trim()
    if (trimmed && !tags.some((t) => t.text.toLowerCase() === trimmed.toLowerCase())) {
      setTags([...tags, { id: Math.random().toString(), text: trimmed }])
      setTagInput("")
    }
  }

  const removeTag = (id: string) => {
    setTags(tags.filter((t) => t.id !== id))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!title.trim()) {
      setError("请输入视频标题")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // 解码videoId以处理URL编码问题
      const decodedVideoId = decodeURIComponent(videoId)

      await submitVideoMetadata(decodedVideoId, {
        title,
        description,
        keywords: keywords.map((k) => k.text),
        tags: tags.map((t) => t.text),
      })

      setSuccess(true)

      // 显示成功消息 2 秒后跳转
      setTimeout(() => {
        router.push("/videos")
      }, 2000)
    } catch (err) {
      setError(err instanceof Error ? err.message : "提交失败，请重试")
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-500 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-100">
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>视频信息已成功提交到Dify知识库！系统将自动跳转到视频列表页面。</AlertDescription>
        </Alert>
      )}

      {/* 视频预览 */}
      {videoUrl && (
        <div className="aspect-video overflow-hidden rounded-lg bg-black">
          <video src={videoUrl} controls className="h-full w-full" poster="/placeholder.svg?height=720&width=1280" />
        </div>
      )}

      {/* 元数据分析状态 */}
      {isRefiningMetadata && (
        <div className="flex items-center justify-center space-x-2 rounded-lg border border-blue-200 bg-blue-50 p-4 text-blue-800 dark:border-blue-800 dark:bg-blue-900 dark:text-blue-100">
          <Loader2 className="h-5 w-5 animate-spin" />
          <p>正在进行AI视频分析，生成详细描述、分类和标签...</p>
        </div>
      )}

      {/* AI分析完成提示 */}
      {hasRefinedMetadata && !isRefiningMetadata && (
        <div className="flex items-center justify-between rounded-lg border border-green-200 bg-green-50 p-4 text-green-800 dark:border-green-800 dark:bg-green-900 dark:text-green-100">
          <div className="flex items-center space-x-2">
            <CheckCircle2 className="h-5 w-5" />
            <p>AI视频分析完成！已生成优化的描述、分类和标签。</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (title && videoUrl && rawDescription) {
                setHasRefinedMetadata(false)
                refineVideoMetadata(title, videoUrl, rawDescription)
              }
            }}
            disabled={isRefiningMetadata || !title || !videoUrl || !rawDescription}
          >
            重新分析
          </Button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <label htmlFor="title" className="text-sm font-medium">
            视频标题
          </label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="输入视频标题"
            disabled={isLoading || isRefiningMetadata || isSubmitting}
            required
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="description" className="text-sm font-medium">
            视频描述
          </label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="输入视频描述"
            rows={5}
            disabled={isLoading || isRefiningMetadata || isSubmitting}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="keywords" className="text-sm font-medium">
            分类
          </label>
          <div className="flex flex-wrap gap-2">
            {keywords.map((keyword) => (
              <Badge key={keyword.id} variant="secondary" className="flex items-center gap-1">
                {keyword.text}
                <button
                  type="button"
                  onClick={() => removeKeyword(keyword.id)}
                  className="ml-1 rounded-full p-0.5 hover:bg-muted"
                  disabled={isSubmitting}
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">移除 {keyword.text}</span>
                </button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              id="keywords"
              value={keywordInput}
              onChange={(e) => setKeywordInput(e.target.value)}
              placeholder="输入分类关键词"
              disabled={isLoading || isRefiningMetadata || isSubmitting}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault()
                  addKeyword()
                }
              }}
            />
            <Button
              type="button"
              onClick={addKeyword}
              disabled={isLoading || isRefiningMetadata || isSubmitting || !keywordInput.trim()}
            >
              添加
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="tags" className="text-sm font-medium">
            标签
          </label>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge key={tag.id} variant="outline" className="flex items-center gap-1">
                {tag.text}
                <button
                  type="button"
                  onClick={() => removeTag(tag.id)}
                  className="ml-1 rounded-full p-0.5 hover:bg-muted"
                  disabled={isSubmitting}
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">移除 {tag.text}</span>
                </button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              id="tags"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              placeholder="输入标签后按回车添加"
              disabled={isLoading || isRefiningMetadata || isSubmitting}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault()
                  addTag()
                }
              }}
            />
            <Button
              type="button"
              onClick={addTag}
              disabled={isLoading || isRefiningMetadata || isSubmitting || !tagInput.trim()}
            >
              添加
            </Button>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.push("/videos")} disabled={isSubmitting}>
            取消
          </Button>
          <Button type="submit" disabled={isLoading || isRefiningMetadata || isSubmitting || !title.trim() || success}>
            {isSubmitting ? "提交中..." : "保存并提交"}
          </Button>
        </div>
      </form>
    </div>
  )
}
