"use client"

import { useState, useEffect } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, Search, Trash2, Loader2, AlertCircle } from "lucide-react"
import { getVideoList, deleteVideo, type Video, type VideoListResponse } from "@/lib/api"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { toast } from "sonner"

// 状态映射
const statusMap = {
  pending: { label: "待处理", variant: "secondary" as const },
  processing: { label: "处理中", variant: "default" as const },
  completed: { label: "已完成", variant: "success" as const },
  failed: { label: "失败", variant: "destructive" as const },
}

// 格式化日期
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return dateString
  }
}

// 格式化分类显示 - 只显示第一个分类
const formatCategories = (categories: string[]) => {
  if (!categories || categories.length === 0) {
    return "未分类"
  }
  // 只返回第一个分类元素
  return categories[0]
}

export function VideoList() {
  const [videos, setVideos] = useState<Video[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(0)

  // 删除状态
  const [deletingVideoId, setDeletingVideoId] = useState<string | null>(null)

  // 获取视频列表
  const fetchVideos = async (page: number = currentPage) => {
    try {
      setLoading(true)
      setError(null)
      const response = await getVideoList(page, pageSize)
      setVideos(response.videos)
      setTotal(response.total)
      setTotalPages(response.total_pages)
      setCurrentPage(response.page)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取视频列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchVideos(1)
  }, [])

  // 处理分页
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      fetchVideos(page)
    }
  }

  // 处理删除视频
  const handleDeleteVideo = async (videoId: string) => {
    try {
      setDeletingVideoId(videoId)

      const result = await deleteVideo(videoId)

      if (result.code === 200) {
        toast.success("视频删除成功", {
          description: `已删除视频: ${videoId}`,
        })

        // 刷新视频列表
        await fetchVideos(currentPage)
      } else {
        throw new Error(result.message || '删除失败')
      }
    } catch (error) {
      console.error('删除视频失败:', error)
      toast.error("删除视频失败", {
        description: error instanceof Error ? error.message : '未知错误',
      })
    } finally {
      setDeletingVideoId(null)
    }
  }

  // 获取所有分类
  const allCategories = Array.from(
    new Set(videos.flatMap(video => video.categories || []))
  ).filter(Boolean)

  // 过滤视频列表（注意：这里是前端过滤，实际项目中建议在后端实现搜索和过滤）
  const filteredVideos = videos.filter((video) => {
    const matchesSearch = video.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "all" ||
      (video.categories && video.categories.includes(categoryFilter))
    const matchesStatus = statusFilter === "all" || video.status === statusFilter
    return matchesSearch && matchesCategory && matchesStatus
  })

  // 加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            className="ml-2"
            onClick={() => window.location.reload()}
          >
            重试
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="animate-fade-in space-y-6">
      <Card className="overflow-hidden">
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索视频..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="分类筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {allCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="pending">待处理</SelectItem>
                  <SelectItem value="processing">处理中</SelectItem>
                  <SelectItem value="completed">已完成</SelectItem>
                  <SelectItem value="failed">失败</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <div className="rounded-md border-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>标题</TableHead>
                <TableHead className="text-left">分类</TableHead>
                <TableHead>更新日期</TableHead>
                <TableHead className="text-left">处理状态</TableHead>
                <TableHead className="w-[80px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVideos.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    暂无视频数据
                  </TableCell>
                </TableRow>
              ) : (
                filteredVideos.map((video) => (
                  <TableRow key={video.id} className="table-row-hover">
                    <TableCell className="font-medium">{video.title}</TableCell>
                    <TableCell className="align-middle">
                      <Badge variant="outline">{formatCategories(video.categories)}</Badge>
                    </TableCell>
                    <TableCell>{formatDate(video.updated_at)}</TableCell>
                    <TableCell className="align-middle">
                      <Badge variant={statusMap[video.status]?.variant || "secondary"}>
                        {statusMap[video.status]?.label || video.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            disabled={deletingVideoId === video.title}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">删除视频</span>
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>确认删除视频</AlertDialogTitle>
                            <AlertDialogDescription>
                              您确定要删除视频 <strong>{video.title}</strong> 吗？
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <div className="py-4">
                            <p className="text-sm text-muted-foreground mb-3">
                              此操作将会：
                            </p>
                            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground mb-4">
                              <li>删除数据库中的视频记录</li>
                              {video.status === 'completed' && (
                                <li>删除知识库中的相关文档</li>
                              )}
                              <li className="text-muted-foreground">MinIO中的视频文件将保留</li>
                            </ul>
                            <p className="text-sm font-medium text-red-600">
                              此操作不可撤销！
                            </p>
                          </div>
                          <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteVideo(video.title)}
                              className="bg-red-600 hover:bg-red-700"
                              disabled={deletingVideoId === video.title}
                            >
                              {deletingVideoId === video.title ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  删除中...
                                </>
                              ) : (
                                '确认删除'
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>

      {/* 分页组件 */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          共 <span className="font-medium">{total}</span> 个视频，
          第 <span className="font-medium">{currentPage}</span> 页，
          共 <span className="font-medium">{totalPages}</span> 页
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage <= 1 || loading}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">上一页</span>
          </Button>

          {/* 页码显示 */}
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum: number
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (currentPage <= 3) {
                pageNum = i + 1
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = currentPage - 2 + i
              }

              return (
                <Button
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handlePageChange(pageNum)}
                  disabled={loading}
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage >= totalPages || loading}
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">下一页</span>
          </Button>
        </div>
      </div>
    </div>
  )
}
