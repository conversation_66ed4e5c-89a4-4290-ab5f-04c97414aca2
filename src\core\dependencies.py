"""
依赖注入模块
Dependency Injection Module

此模块负责管理所有服务实例的创建和依赖注入，
确保配置和服务实例化的清晰分离。
"""

from functools import lru_cache
from typing import Annotated

from fastapi import Depends

# 导入配置
from src.config.settings import (
    minio_settings,
    dify_settings,
    llm_settings,
    alibaba_cloud_settings,
    video_api_settings
)


# ===== 服务工厂函数 =====

@lru_cache()
def get_minio_service():
    """
    获取MinIO服务实例
    使用 lru_cache 确保单例模式
    """
    from src.services.minio_service import MinioService
    return MinioService(minio_settings)


@lru_cache()
def get_dify_service():
    """
    获取Dify服务实例
    使用 lru_cache 确保单例模式
    """
    from src.services.dify_service import DifyService
    return DifyService(dify_settings)


@lru_cache()
def get_llm_service():
    """
    获取LLM服务实例
    使用 lru_cache 确保单例模式
    """
    from src.services.llm_service import LLMService
    return LLMService(llm_settings)


@lru_cache()
def get_alibaba_cloud_service():
    """
    获取阿里云服务实例
    使用 lru_cache 确保单例模式
    """
    from src.document_rag.ali_service import AlibabaCloudService
    return AlibabaCloudService(alibaba_cloud_settings)


# ===== FastAPI 依赖注入类型注解 =====

# 为了避免循环导入，使用字符串类型注解
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.services.minio_service import MinioService
    from src.services.dify_service import DifyService
    from src.services.llm_service import LLMService

# MinIO服务依赖
MinioServiceDep = Annotated[
    "MinioService",
    Depends(get_minio_service)
]

# Dify服务依赖
DifyServiceDep = Annotated[
    "DifyService",
    Depends(get_dify_service)
]

# LLM服务依赖
LLMServiceDep = Annotated[
    "LLMService",
    Depends(get_llm_service)
]

# 阿里云服务依赖
AlibabaCloudServiceDep = Annotated[
    "AlibabaCloudService",
    Depends(get_alibaba_cloud_service)
]


# ===== 配置依赖注入 =====

def get_video_api_settings():
    """获取视频API配置"""
    return video_api_settings


def get_minio_settings():
    """获取MinIO配置"""
    return minio_settings


def get_dify_settings():
    """获取Dify配置"""
    return dify_settings


def get_llm_settings():
    """获取LLM配置"""
    return llm_settings


# 配置依赖类型注解
VideoAPISettingsDep = Annotated[
    "VideoAPISettings",
    Depends(get_video_api_settings)
]

MinioSettingsDep = Annotated[
    "MinIOSettings",
    Depends(get_minio_settings)
]

DifySettingsDep = Annotated[
    "DifySettings",
    Depends(get_dify_settings)
]

LLMSettingsDep = Annotated[
    "LLMSettings",
    Depends(get_llm_settings)
]


# ===== 便捷函数 =====

def create_minio_service():
    """创建新的MinIO服务实例（非单例）"""
    from src.services.minio_service import MinioService
    return MinioService(minio_settings)


def create_dify_service():
    """创建新的Dify服务实例（非单例）"""
    from src.services.dify_service import DifyService
    return DifyService(dify_settings)


def create_llm_service():
    """创建新的LLM服务实例（非单例）"""
    from src.services.llm_service import LLMService
    return LLMService(llm_settings)


# ===== 服务健康检查 =====

async def check_services_health():
    """检查所有服务的健康状态"""
    health_status = {}
    
    try:
        minio_service = get_minio_service()
        health_status["minio"] = await minio_service.test_connection()
    except Exception as e:
        health_status["minio"] = {"status": "error", "error": str(e)}
    
    try:
        dify_service = get_dify_service()
        # 这里可以添加Dify服务的健康检查
        health_status["dify"] = {"status": "ok"}
    except Exception as e:
        health_status["dify"] = {"status": "error", "error": str(e)}
    
    try:
        llm_service = get_llm_service()
        # 这里可以添加LLM服务的健康检查
        health_status["llm"] = {"status": "ok"}
    except Exception as e:
        health_status["llm"] = {"status": "error", "error": str(e)}
    
    return health_status


# ===== 清理函数 =====

def clear_service_cache():
    """清理服务缓存，强制重新创建服务实例"""
    get_minio_service.cache_clear()
    get_dify_service.cache_clear()
    get_llm_service.cache_clear()
    get_alibaba_cloud_service.cache_clear()


# ===== 向后兼容 =====

# 为了向后兼容，提供旧的接口
def get_minio_service_legacy():
    """向后兼容的MinIO服务获取函数"""
    return get_minio_service()


# 全局服务实例（向后兼容）
minio_service_global = get_minio_service()
dify_service_global = get_dify_service()
