from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from src.core.database import init_db, close_db
from src.core.config import settings

# 视频处理api
from src.video_rag.api import video_router



# 定义一个异步上下文管理器来管理应用生命周期中的数据库连接
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    异步上下文管理器用于管理应用生命周期中的数据库连接。
    在应用生命周期中，如果有异步任务或者其他异步操作，则必须使用该管理器来处理它们。
    """
    print("初始化数据库连接...")
    await init_db()
    try:
        yield
    finally:
        print("关闭数据库连接...")
        await close_db()
        
app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def read_root():
    return {"message": "Hello, World!"}

# 引入视频处理api
include_router = app.include_router(video_router, prefix="/api/video")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)