from typing import Annotated, Set
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, status, Query, Body
from pydantic_settings import BaseSettings, SettingsConfigDict
from src.services.minio_service import MinioService, get_minio_service
from src.config.setting import DocExtensionsSettings, get_allow_file_extensions
from src.models.document import Document, TaskStatus
import os


document_rag_router = APIRouter()


minio_service_dep = Annotated[MinioService, Depends(get_minio_service)]

# 支持的文档格式
allow_file_extensions_dep = Annotated[DocExtensionsSettings, Depends(get_allow_file_extensions)]


@document_rag_router.post("/upload_document")
async def upload_document(minio_service: minio_service_dep, 
                          file_extension: allow_file_extensions_dep,
                          file: UploadFile = File(...)
                          ):
    """
    用户上传文档到oss，上传成功后服务会立即响应task_id，并在后台异步执行文档解析
    """
    
    
    file_name = file.filename
    if not file_name:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="文件名不能为空")
    
    # 检查文件扩展名
    file_extension = file_name.split(".")[-1].lower() if '.' in file_name else ""
    
    if file_extension not in file_extension.ALLOW_FILE_EXTENSIONS:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"不支持的文件格式: {file_extension}"
                            )
    # 异步读取文件流
    file_stream = await file.read()
    
    file_length = len(file_stream)
    # 验证文件大小
    MAX_FILE_SIZE = 1024 * 1024 * 150  # 150MB
    if file_length > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件大小超过限制，最大允许大小为{MAX_FILE_SIZE}B"
        )
    try:
        # 上传文件至minio
        document_url = await minio_service.upload_file(
            bucket_name="zhanshu",
            object_name=f'docs/{file_name}',
            file_stream=file_stream,
            length=file_length,
            content_type=file.content_type
        )
        # 创建文档记录,检查文件是否存在,不存在则创建记录
        document_entry = await Document.get_or_none(original_filename=file_name)
        if not document_entry:
            document_entry = await Document.create(
                original_filename=file_name,
                minio_path=document_url
            )
        else:
            document_entry.minio_path = document_url
            status = TaskStatus.WATTING
            ali_task_id = ""
            ali_task_status = ""
            dify_dataset_id = ""
            dify_document_id = ""
            dify_batch_id = ""
            dify_indexing_status = ""
            error_message = ""
            
            document_entry.save()
            
        return {"code": 201, "message": "上传成功", "data": document_entry.to_dict()}
    
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"上传失败: {e}")
    
    
    