import json
import asyncio
import urllib.request
import urllib.parse
import urllib.error
from typing import Dict, List, Optional
from src.config.settings import settings

class DifyService:
    def __init__(self):
        """
        初始化Dify服务
        """
        self.api_key = settings.dify.DIFY_API_KEY
        self.base_url = settings.dify.DIFY_API_BASE
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.knowledge_base_name = "视频知识库"
        self.knowledge_base_id = None

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """
        发送HTTP请求到Dify API

        Args:
            method: HTTP方法 (GET, POST, PATCH等)
            endpoint: API端点
            data: 请求数据

        Returns:
            API响应数据
        """
        url = f"{self.base_url}{endpoint}"

        # 在异步函数中运行同步的urllib请求
        def make_sync_request():
            try:
                # 准备请求数据
                request_data = None
                if data:
                    request_data = json.dumps(data).encode('utf-8')

                # 创建请求
                req = urllib.request.Request(
                    url=url,
                    data=request_data,
                    headers=self.headers,
                    method=method
                )

                # 发送请求
                with urllib.request.urlopen(req) as response:
                    response_data = response.read().decode('utf-8')
                    return json.loads(response_data)

            except urllib.error.HTTPError as e:
                error_text = e.read().decode('utf-8') if e.fp else str(e)
                raise Exception(f"Dify API请求失败: {e.code} - {error_text}")
            except Exception as e:
                raise Exception(f"Dify API请求失败: {str(e)}")

        # 在线程池中运行同步请求以避免阻塞事件循环
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, make_sync_request)

    async def list_knowledge_bases(self) -> List[Dict]:
        """
        获取所有知识库列表

        Returns:
            知识库列表
        """
        try:
            response = await self._make_request("GET", "/datasets")
            return response.get("data", [])
        except Exception as e:
            raise Exception(f"获取知识库列表失败: {str(e)}")

    async def find_knowledge_base_by_name(self, name: str) -> Optional[Dict]:
        """
        根据名称查找知识库

        Args:
            name: 知识库名称

        Returns:
            知识库信息，如果不存在则返回None
        """
        try:
            knowledge_bases = await self.list_knowledge_bases()
            for kb in knowledge_bases:
                if kb.get("name") == name:
                    return kb
            return None
        except Exception as e:
            raise Exception(f"查找知识库失败: {str(e)}")

    async def create_knowledge_base(self, name: str, description: str = "") -> Dict:
        """
        创建新的知识库，使用系统默认模型和混合检索

        Args:
            name: 知识库名称
            description: 知识库描述

        Returns:
            创建的知识库信息
        """
        try:
            # 创建知识库的基本配置
            data = {
                "name": name,
                "description": description,
                "permission": "only_me",
                "indexing_technique": "high_quality",
                "retrieval_model": {
                    "search_method": "hybrid_search",  # 使用混合检索
                    "reranking_enable": False,
                    "reranking_mode": None,
                    "reranking_model": {
                        "reranking_provider_name": "",
                        "reranking_model_name": ""
                    },
                    "weights": None,
                    "top_k": 3,
                    "score_threshold_enabled": False,
                    "score_threshold": None
                }
                # 不指定embedding_model和embedding_model_provider，使用系统默认
            }

            response = await self._make_request("POST", "/datasets", data)
            return response
        except Exception as e:
            raise Exception(f"创建知识库失败: {str(e)}")

    async def ensure_knowledge_base_exists(self) -> str:
        """
        确保视频知识库存在，如果不存在则创建

        Returns:
            知识库ID
        """
        try:
            # 查找现有知识库
            existing_kb = await self.find_knowledge_base_by_name(self.knowledge_base_name)

            if existing_kb:
                self.knowledge_base_id = existing_kb["id"]
                return self.knowledge_base_id

            # 创建新知识库
            new_kb = await self.create_knowledge_base(
                name=self.knowledge_base_name,
                description="用于存储和检索视频内容的知识库，包含视频标题、描述、分类和标签信息"
            )

            self.knowledge_base_id = new_kb["id"]
            return self.knowledge_base_id

        except Exception as e:
            raise Exception(f"确保知识库存在失败: {str(e)}")

    async def create_document(self, knowledge_base_id: str, name: str, content: str) -> Dict:
        """
        在知识库中创建文档

        Args:
            knowledge_base_id: 知识库ID
            name: 文档名称
            content: 文档内容

        Returns:
            创建的文档信息
        """
        try:
            print(f"创建文档: {name}")
            print(f"知识库ID: {knowledge_base_id}")
            print(f"内容长度: {len(content)}字符")
            print(f"内容预览: {content[:200]}...")

            data = {
                "name": name,
                "text": content,
                "indexing_technique": "high_quality",
                "process_rule": {
                    "mode": "custom",
                    "rules": {
                        "pre_processing_rules": [
                            {
                                "id": "remove_extra_spaces",
                                "enabled": True
                            },
                            {
                                "id": "remove_urls_emails",
                                "enabled": False
                            }
                        ],
                        "segmentation": {
                            "separator": "\n\n",
                            "max_tokens": 1000
                        }
                    }
                }
            }

            endpoint = f"/datasets/{knowledge_base_id}/document/create-by-text"
            print(f"调用API端点: {endpoint}")

            response = await self._make_request("POST", endpoint, data)
            print(f"文档创建响应: {response}")

            return response

        except Exception as e:
            print(f"创建文档失败: {str(e)}")
            raise Exception(f"创建文档失败: {str(e)}")

    async def submit_video_to_knowledge_base(self, video_data: Dict) -> Dict:
        """
        将视频信息提交到知识库

        Args:
            video_data: 视频数据，包含title, description, categories, tags, video_url

        Returns:
            提交结果
        """
        try:
            # 确保知识库存在
            kb_id = await self.ensure_knowledge_base_exists()

            # 格式化视频数据为结构化JSON文本
            formatted_content = self._format_video_content(video_data)

            # 创建文档名称
            document_name = f"视频_{video_data.get('title', 'unknown')}"

            # 创建文档
            document = await self.create_document(
                knowledge_base_id=kb_id,
                name=document_name,
                content=formatted_content
            )

            return {
                "success": True,
                "knowledge_base_id": kb_id,
                "document_id": document.get("document", {}).get("id"),
                "message": "视频信息已成功提交到知识库"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"提交到知识库失败: {str(e)}"
            }

    async def delete_document_from_knowledge_base(self, knowledge_base_id: str, document_id: str) -> Dict:
        """
        从知识库中删除文档

        Args:
            knowledge_base_id: 知识库ID
            document_id: 文档ID

        Returns:
            删除结果
        """
        try:
            print(f"删除知识库文档: KB={knowledge_base_id}, Doc={document_id}")

            # 调用Dify API删除文档
            endpoint = f"/datasets/{knowledge_base_id}/documents/{document_id}"
            await self._make_request("DELETE", endpoint)

            print(f"知识库文档删除成功: {document_id}")

            return {
                "success": True,
                "message": "知识库文档删除成功"
            }

        except Exception as e:
            print(f"删除知识库文档失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"删除知识库文档失败: {str(e)}"
            }

    async def find_document_by_video_title(self, video_title: str) -> Dict:
        """
        根据视频标题查找知识库中的文档

        Args:
            video_title: 视频标题

        Returns:
            文档信息或None
        """
        try:
            # 确保知识库存在
            kb_id = await self.ensure_knowledge_base_exists()

            # 获取知识库中的所有文档
            documents_response = await self._make_request("GET", f"/datasets/{kb_id}/documents")
            documents = documents_response.get('data', [])

            # 查找匹配的文档
            document_name = f"视频_{video_title}"
            for doc in documents:
                if doc.get('name') == document_name:
                    return {
                        "found": True,
                        "knowledge_base_id": kb_id,
                        "document_id": doc.get('id'),
                        "document_name": doc.get('name')
                    }

            return {
                "found": False,
                "knowledge_base_id": kb_id,
                "message": f"未找到文档: {document_name}"
            }

        except Exception as e:
            print(f"查找文档失败: {str(e)}")
            return {
                "found": False,
                "error": str(e),
                "message": f"查找文档失败: {str(e)}"
            }

    def _format_video_content(self, video_data: Dict) -> str:
        """
        将视频数据格式化为纯JSON格式，不包含任何多余内容

        Args:
            video_data: 标准化的视频数据

        Returns:
            纯JSON格式的文本内容，不包含任何多余内容
        """
        title = video_data.get("title", "")
        description = video_data.get("description", "")
        categories = video_data.get("categories", [])
        tags = video_data.get("tags", [])
        video_url = video_data.get("video_url", "")

        # 返回纯JSON格式，确保格式正确
        json_content = json.dumps({
            "title": title,
            "description": description,
            "categories": categories,
            "tags": tags,
            "video_url": video_url
        }, ensure_ascii=False, indent=2)

        print(f"格式化的内容长度: {len(json_content)}字符")
        print(f"格式化的内容: {json_content}")

        return json_content

# 创建全局实例
dify_service = DifyService()