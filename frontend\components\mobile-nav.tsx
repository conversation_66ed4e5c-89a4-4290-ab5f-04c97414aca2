"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import { Film, Menu, LayoutDashboard, Video } from "lucide-react"

export default function MobileNav() {
  const [open, setOpen] = useState(false)
  const pathname = usePathname()

  const routes = [
    {
      label: "仪表盘",
      icon: LayoutDashboard,
      href: "/",
      active: pathname === "/",
    },
    {
      label: "视频列表",
      icon: Film,
      href: "/videos",
      active: pathname === "/videos" || pathname.startsWith("/videos/"),
    },
  ]

  return (
    <div className="flex h-16 items-center border-b bg-background/80 px-4 backdrop-blur-sm md:hidden">
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="mr-2">
            <Menu className="h-5 w-5" />
            <span className="sr-only">打开菜单</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <div className="flex h-16 items-center border-b px-4">
            <Link href="/" className="flex items-center gap-2 font-semibold" onClick={() => setOpen(false)}>
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
                <Video className="h-4 w-4" />
              </div>
              <span className="text-lg font-bold tracking-tight">VideoHub</span>
            </Link>
          </div>
          <div className="flex-1 overflow-auto py-4">
            <nav className="grid items-start gap-2 px-2 text-sm font-medium">
              {routes.map((route) => (
                <Link
                  key={route.href}
                  href={route.href}
                  className={cn(
                    "group flex items-center gap-3 rounded-lg px-3 py-2.5 transition-all hover:bg-muted",
                    route.active ? "bg-primary/10 text-primary" : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => setOpen(false)}
                >
                  <div
                    className={cn(
                      "flex h-7 w-7 items-center justify-center rounded-md transition-colors",
                      route.active
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground group-hover:bg-primary/20 group-hover:text-primary",
                    )}
                  >
                    <route.icon className="h-4 w-4" />
                  </div>
                  <span>{route.label}</span>
                </Link>
              ))}
            </nav>
          </div>
          <div className="mt-auto border-t p-4"></div>
        </SheetContent>
      </Sheet>
      <Link href="/" className="flex items-center gap-2 font-semibold">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
          <Video className="h-4 w-4" />
        </div>
        <span className="text-lg font-bold tracking-tight">VideoHub</span>
      </Link>
    </div>
  )
}
