# 阿里云文档解析服务
import asyncio
from src.config.settings import settings

# 阿里云文档解析包
from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_tea_util import models as util_models
from alibabacloud_credentials.client import Client as CredClient
    
ali_service_settings = AliServiceSettings()
    
class AliDocService:
    def __init__(self):
        """
        初始化AliDocService
        """
        self.config = open_api_models.Config(
            type="access_key"
            access_key_id=ali_service_settings.ACCESS_KEY_ID,
            access_key_secret=ali_service_settings.ACCESS_KEY_SECRET,
            endpoint=ali_service_settings.ENDPOINT
        )
        
        # 初始化client
        self.client = docmind_api20220711Client(self.config)
    
    async def submit_file(self, file_url: str, file_name: str):
        """
        异步提交文件到阿里云文档解析服务
        Args:
            file_url (str): 文件路径
            file_name (str): 文件名称必须包含后缀
            file_name_extension (str): 文件后缀,和file_name二选一，正常使用文件名即可
        Returns:
            job_id (str): 任务ID
        """
        
        # 传参
        request = docmind_api20220711_models.SubmitDocParserJobRequest(
            file_url=file_url,
            file_name=file_name,
            formula_enhancement=True,
            llm_enhancement=True,
            # 可进行OSS托管保证文件不出内网，具体查看文档
        )
        
        # 提交任务
        try:
            # 使用await.to_thread 将client提交到新线程等待保证主线程空闲
            response = await asyncio.to_thread(self.client.submit_doc_parser_job, request)
            print(response)
        except Exception as e:
            UtilClient.assert_as_string(e.message)