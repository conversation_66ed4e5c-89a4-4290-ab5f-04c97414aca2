"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Film, Eye, Clock, BarChart2 } from "lucide-react"

export function VideoStatsCards() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="stat-card hover-card overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">视频总数</p>
              <div className="mt-1 flex items-baseline">
                <h3 className="text-3xl font-bold tracking-tight">1,284</h3>
                <span className="ml-2 text-xs font-medium text-green-500">+12.5%</span>
              </div>
              <p className="mt-2 text-xs text-muted-foreground">较上月增长</p>
            </div>
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Film className="h-6 w-6 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>
      <Card className="stat-card hover-card overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">总观看次数</p>
              <div className="mt-1 flex items-baseline">
                <h3 className="text-3xl font-bold tracking-tight">2.4M</h3>
                <span className="ml-2 text-xs font-medium text-green-500">+18.2%</span>
              </div>
              <p className="mt-2 text-xs text-muted-foreground">较上月增长</p>
            </div>
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Eye className="h-6 w-6 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>
      <Card className="stat-card hover-card overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">总时长</p>
              <div className="mt-1 flex items-baseline">
                <h3 className="text-3xl font-bold tracking-tight">428小时</h3>
                <span className="ml-2 text-xs font-medium text-green-500">+5.7%</span>
              </div>
              <p className="mt-2 text-xs text-muted-foreground">较上月增长</p>
            </div>
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Clock className="h-6 w-6 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>
      <Card className="stat-card hover-card overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">平均观看时长</p>
              <div className="mt-1 flex items-baseline">
                <h3 className="text-3xl font-bold tracking-tight">12分钟</h3>
                <span className="ml-2 text-xs font-medium text-green-500">+2.3%</span>
              </div>
              <p className="mt-2 text-xs text-muted-foreground">较上月增长</p>
            </div>
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <BarChart2 className="h-6 w-6 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
