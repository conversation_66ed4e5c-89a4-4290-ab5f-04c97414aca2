<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>Video Analysis API Test</h1>
    <button onclick="testAnalyzeVideo()">Test Analyze Video API</button>
    <button onclick="testStandardizedSubmit()">Test Standardized JSON Submit</button>
    <div id="result"></div>

    <script>
        async function testAnalyzeVideo() {
            // First get the actual video info from the database
            const videoInfoResponse = await fetch('http://127.0.0.1:8000/api/video/info/test.mp4', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                }
            });

            const videoInfo = await videoInfoResponse.json();

            if (videoInfo.code !== 200) {
                document.getElementById('result').innerHTML = 'Error getting video info: ' + videoInfo.message;
                return;
            }

            const requestData = {
                "title": videoInfo.data.title,
                "video_url": videoInfo.data.video_url,
                "description": videoInfo.data.description
            };

            try {
                const response = await fetch('http://127.0.0.1:8000/api/video/analyze_video', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }

        async function testStandardizedSubmit() {
            // Test the standardized JSON format submission
            const standardizedPayload = {
                "title": "test2.mp4",
                "description": "视频介绍了百度网盘新上线的AI功能，包括文字生成图片、图片生成图片、图像风格转换和图像高清修复。展示了使用这些功能生成的图片效果，如动漫风格的女性角色和真实照片。详细演示了在百度网盘中使用这些功能的步骤，包括选择模型、输入提示词、上传图片等。还提到了Minimax MCP平台，展示了其界面和功能选项。视频内容以屏幕录制为主，穿插动画插图，风格偏向科技感和实用性介绍。",
                "categories": [
                    "人工智能",
                    "百度网盘",
                    "图像处理"
                ],
                "tags": [
                    "百度网盘",
                    "AI功能",
                    "文字生成图片",
                    "图片生成图片",
                    "图像风格转换",
                    "图像高清修复",
                    "Minimax MCP平台",
                    "动漫风格",
                    "真实照片",
                    "科技感"
                ],
                "video_url": "http://119.3.237.14:9000/zhanshu-video/test2.mp4"
            };

            try {
                console.log('Submitting standardized payload:', JSON.stringify(standardizedPayload, null, 2));

                const response = await fetch('http://127.0.0.1:8000/api/video/test_dify_format', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(standardizedPayload)
                });

                const data = await response.json();
                document.getElementById('result').innerHTML = '<h3>Standardized JSON Test Result:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
