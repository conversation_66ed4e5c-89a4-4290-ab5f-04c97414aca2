from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Set

# 支持的文档格式
class DocExtensionsSettings(BaseSettings):
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
    
    # 文档集合
    SUPPORTED_DOCUMENT_EXTENSIONS: Set[str] = {"pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "xlsm"}
    
    #图片集合
    SUPPORTED_IMAGE_EXTENSIONS: Set[str] = {"jpg", "jpeg", "png", "bmp", "gif"}
    
    # 取并集
    ALLOW_FILE_EXTENSIONS: Set[str] = \
        SUPPORTED_DOCUMENT_EXTENSIONS.union(SUPPORTED_IMAGE_EXTENSIONS)

do_ext_settings = DocExtensionsSettings()

def get_allow_file_extensions() -> Set[str]:
    return do_ext_settings